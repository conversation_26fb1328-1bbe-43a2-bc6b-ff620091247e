# Elastic impedance calculation, constant Vs/Vp ratio (Equation 4 for 'a')

# Set parameters
Xt = np.radians(13)  # Angle in radians
k = 0.243            # Vs^2 / Vp^2 ratio

# Input logs: DT, DTS, RHOB
# DT = P-wave sonic log (us/ft), DTS = S-wave sonic log (us/ft), RHOB = Density log (g/cc)
# Convert DT and DTS to velocities in m/s:
# (You may need to convert your DT/DTS from us/ft to us/m, then to s/m)

# Example (assuming DT, DTS in us/ft):
# 1 ft = 0.3048 m
# P_WAVE = 1e6 / (DT * 0.3048)  # in m/s
# S_WAVE = 1e6 / (DTS * 0.3048) # in m/s

P_WAVE = 1e6 / DT  # m/s (if DT in us/m)
S_WAVE = 1e6 / DTS # m/s (if DTS in us/m)
VP_VS = P_WAVE/S_WAVE

# Coefficients using Equation 4 from <PERSON><PERSON><PERSON> (2002) and <PERSON> (1999)
sin2 = np.sin(Xt)**2
a = 1 + sin2                 # Equation 4: a = 1 + sin^2(theta)
b = -8 * k * sin2
c = 1 - 4 * k * sin2

# Elastic Impedance calculation (vectorized for logs)
EI = (P_WAVE ** a) * (S_WAVE ** b) * (RHOB ** c)


# Elastic impedance calculation with sample-by-sample Vs/Vp ratio (K)
# Set parameters
Xt = np.radians(13)  # Angle in radians

# Input logs: DT, DTS, RHOB (already in compatible units)
P_WAVE = 1e6 / DT   # m/s
S_WAVE = 1e6 / DTS  # m/s

# Calculate K at each sample
K_LOG = (S_WAVE ** 2) / (P_WAVE ** 2)
K = np.nanmean(K_LOG)

# Coefficients (a is scalar, b and c are arrays)
sin2 = np.sin(Xt) ** 2
a = 1 + sin2                 # Equation 4: a = 1 + sin^2(theta)
b = -8 * K * sin2
c = 1 - 4 * K * sin2

# Elastic Impedance calculation (vectorized)
EI = (P_WAVE ** a) * (S_WAVE ** b) * (RHOB ** c)


# Normalized Elastic Impedance (Equation 9 with exponents from Equation 4)
import numpy as np

# Parameters
ag = 13
Xt = np.radians(ag)  # Angle in radians

# Logs
P_WAVE = 1e6 / DT   # Vp, m/s
S_WAVE = 1e6 / DTS  # Vs, m/s
RHOB = RHOB         # Density log

# Calculate K at each sample
K = (S_WAVE ** 2) / (P_WAVE ** 2)

# Exponents (Equation 4)
sin2 = np.sin(Xt) ** 2
a = 1 + sin2                 # Equation 4: a = 1 + sin^2(theta)
b = -8 * K * sin2
c = 1 - 4 * K * sin2

# Reference values for normalization
alpha_o = np.mean(P_WAVE)
beta_o  = np.mean(S_WAVE)
rho_o   = np.mean(RHOB)

# Normalized EI (Equation 9, with exponents from Equation 4)
EI_norm_eq4 = (
    alpha_o * rho_o *
    ((P_WAVE / alpha_o) ** a) *
    ((S_WAVE / beta_o) ** b) *
    ((RHOB / rho_o) ** c)
)


<!-- # Set parameters
Xt = np.radians(30)
k = 0.243
ao = 2865
bo = 1410
co = 2.35
AIo = 6744
SIo = 3319 -->

<!-- # Convert RHOB from g/cc to kg/m^3
RHOB_kgm3 = RHOB * 1000.0

# Calculate P-wave and S-wave velocities
P_WAVE = 1e6 / DT  # m/s
S_WAVE = 1e6 / DTS # m/s

# Shear modulus (G), Bulk modulus (K), Lame's first parameter (lambda), and P-wave modulus (M) in GPa
GSAT = RHOB_kgm3 * (S_WAVE ** 2) / 1e9           # GPa
KSAT = (RHOB_kgm3 * (P_WAVE ** 2) - (4.0/3.0) * GSAT * 1e9) / 1e9  # GPa
LSAT = KSAT - (2.0 / 3.0) * GSAT                 # GPa
MSAT = KSAT + (4.0 / 3.0) * GSAT                 # GPa -->
