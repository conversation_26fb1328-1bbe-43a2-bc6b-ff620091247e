# -*- coding: utf-8 -*-
"""
Test script to verify the refactored EEI analysis modules work correctly.

This script tests the basic functionality of the new modular components
to ensure they produce expected results.

Author: Refactoring Test
Created: 2024
Version: 1.0.0
"""

import numpy as np
import sys
import os

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_config_module():
    """Test the configuration module."""
    print("🧪 Testing EEI Configuration Module...")

    try:
        from eei_config import EEIConfig, LOG_KEYWORDS, ANALYSIS_PARAMS

        # Test log keywords retrieval
        keywords = EEIConfig.get_log_keywords()
        assert 'DT' in keywords
        assert 'RHOB' in keywords
        print("✅ Log keywords retrieval: PASSED")

        # Test analysis parameters
        cpei_params = EEIConfig.get_analysis_params('CPEI')
        assert 'n_range' in cpei_params
        assert 'phi_range' in cpei_params
        print("✅ Analysis parameters retrieval: PASSED")

        # Test error message formatting
        error_msg = EEIConfig.get_error_message('insufficient_data', min_points=10)
        assert '10' in error_msg
        print("✅ Error message formatting: PASSED")

        print("✅ Configuration module: ALL TESTS PASSED\n")
        return True

    except Exception as e:
        print(f"❌ Configuration module test failed: {str(e)}\n")
        return False

def test_data_processing_module():
    """Test the data processing module."""
    print("🧪 Testing EEI Data Processing Module...")

    try:
        from eei_data_processing import EEIDataProcessor

        # Test correlation calculation
        x = np.array([1, 2, 3, 4, 5])
        y = np.array([2, 4, 6, 8, 10])
        correlation = EEIDataProcessor.calculate_correlation_safe(x, y)
        assert abs(correlation - 1.0) < 1e-10  # Perfect correlation
        print("✅ Correlation calculation: PASSED")

        # Test with NaN values
        x_nan = np.array([1, 2, np.nan, 4, 5])
        y_nan = np.array([2, 4, 6, np.nan, 10])
        correlation_nan = EEIDataProcessor.calculate_correlation_safe(x_nan, y_nan)
        assert np.isfinite(correlation_nan)  # Should handle NaN values
        print("✅ NaN handling in correlation: PASSED")

        # Test nearest index finding
        array = np.array([1.0, 2.5, 4.0, 6.5, 8.0])
        index = EEIDataProcessor.find_nearest_index(array, 3.0)
        assert index == 1  # Closest to 2.5
        print("✅ Nearest index finding: PASSED")

        # Test array validation
        arrays = [np.array([1, 2, 3]), np.array([4, 5, 6]), np.array([7, 8, 9])]
        validation = EEIDataProcessor.validate_array_compatibility(arrays)
        assert validation['valid'] == True
        print("✅ Array validation: PASSED")

        # Test array statistics
        test_array = np.array([1, 2, 3, 4, 5, np.nan, 7, 8, 9, 10])
        stats = EEIDataProcessor.calculate_array_statistics(test_array)
        print(f"Debug: stats = {stats}")
        assert stats['finite_count'] == 9  # 9 finite values
        expected_mean = np.mean([1, 2, 3, 4, 5, 7, 8, 9, 10])  # Calculate expected mean
        assert abs(stats['mean'] - expected_mean) < 1e-10
        print("✅ Array statistics: PASSED")

        print("✅ Data processing module: ALL TESTS PASSED\n")
        return True

    except Exception as e:
        print(f"❌ Data processing module test failed: {str(e)}\n")
        return False

def test_calculation_engine_module():
    """Test the calculation engine module."""
    print("🧪 Testing EEI Calculation Engine Module...")

    try:
        from eei_calculation_engine import EEICalculationEngine

        # Create synthetic test data
        n_points = 100
        pvel = np.random.uniform(2000, 4000, n_points)
        svel = np.random.uniform(1000, 2000, n_points)
        rhob = np.random.uniform(2.0, 2.8, n_points)
        target = np.random.uniform(0, 100, n_points)

        # Test input validation
        try:
            EEICalculationEngine._validate_optimization_inputs(
                pvel, svel, rhob, target, "TEST"
            )
            print("✅ Input validation: PASSED")
        except ValueError:
            print("❌ Input validation: FAILED")
            return False

        # Test k value determination
        k_calc = EEICalculationEngine._determine_k_value(pvel, svel, 1, None)
        assert isinstance(k_calc, float) and k_calc > 0
        print("✅ K value calculation: PASSED")

        k_const = EEICalculationEngine._determine_k_value(pvel, svel, 2, 0.3)
        assert k_const == 0.3
        print("✅ K value constant: PASSED")

        # Test correlation calculation (using data processor)
        from eei_data_processing import EEIDataProcessor
        corr = EEIDataProcessor.calculate_correlation_safe(pvel, target)
        assert isinstance(corr, (int, float))
        print("✅ Correlation integration: PASSED")

        print("✅ Calculation engine module: ALL TESTS PASSED\n")
        return True

    except Exception as e:
        print(f"❌ Calculation engine module test failed: {str(e)}\n")
        return False

def test_main_file_integration():
    """Test that the main file can import the new modules."""
    print("🧪 Testing Main File Integration...")

    try:
        # Test that imports work
        from eei_calculation_engine import EEICalculationEngine
        from eei_data_processing import EEIDataProcessor
        from eei_config import EEIConfig

        print("✅ Module imports: PASSED")

        # Test that wrapper functions exist (would need actual main file)
        # This is a placeholder for integration testing
        print("✅ Main file integration: BASIC TESTS PASSED\n")
        return True

    except Exception as e:
        print(f"❌ Main file integration test failed: {str(e)}\n")
        return False

def run_all_tests():
    """Run all refactoring tests."""
    print("🚀 Starting EEI Refactoring Tests...\n")

    tests = [
        test_config_module,
        test_data_processing_module,
        test_calculation_engine_module,
        test_main_file_integration
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        if test():
            passed += 1

    print(f"📊 Test Results: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 ALL TESTS PASSED! Refactoring appears successful.")
        return True
    else:
        print("⚠️  Some tests failed. Please review the refactoring.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
