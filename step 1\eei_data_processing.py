# -*- coding: utf-8 -*-
"""
EEI Data Processing Utilities Module

This module contains pure data processing utilities with no UI dependencies.
All functions are stateless and focused on data manipulation and analysis.

Author: Refactored from a7_load_multilas_EEI_XCOR_PLOT_Final.py
Created: 2024
Version: 1.0.0
"""

import numpy as np
import logging
from typing import Dict, List, Tuple, Optional, Union, Any

# Configure module-specific logger
logger = logging.getLogger(__name__)

class EEIDataProcessor:
    """
    Pure data processing utilities for EEI analysis.

    This class contains only stateless data processing methods with no UI dependencies.
    All methods are static to emphasize the stateless nature.
    """

    @staticmethod
    def calculate_correlation_safe(x: np.ndarray, y: np.ndarray) -> float:
        """
        Calculate correlation coefficient with comprehensive error handling.

        This function mimics MATLAB's corr function with 'rows' set to 'complete'.

        Args:
            x: First data array
            y: Second data array

        Returns:
            Correlation coefficient or NaN if calculation fails

        Raises:
            None - Returns NaN for any error condition
        """
        try:
            # Convert inputs to numpy arrays and ensure they are numeric
            x = np.asarray(x, dtype=float)
            y = np.asarray(y, dtype=float)

            # Check if arrays are empty or contain only non-finite values
            if x.size == 0 or y.size == 0:
                logger.warning("calculate_correlation_safe: Empty arrays provided")
                return np.nan

            if not np.isfinite(x).any() or not np.isfinite(y).any():
                logger.warning("calculate_correlation_safe: Arrays contain no finite values")
                return np.nan

            # Create mask for valid (finite) values
            mask = np.isfinite(x) & np.isfinite(y)

            if np.sum(mask) < 2:
                logger.warning("calculate_correlation_safe: Less than 2 valid data points")
                return np.nan

            x_clean = x[mask]
            y_clean = y[mask]

            # Check for constant arrays (no variance)
            if np.all(x_clean == x_clean[0]) or np.all(y_clean == y_clean[0]):
                logger.warning("calculate_correlation_safe: One or both arrays are constant (no variance)")
                return np.nan

            return np.corrcoef(x_clean, y_clean)[0, 1]

        except (ValueError, TypeError) as e:
            logger.error(f"calculate_correlation_safe: Error processing inputs - {str(e)}")
            return np.nan
        except Exception as e:
            logger.error(f"calculate_correlation_safe: Unexpected error - {str(e)}")
            return np.nan

    @staticmethod
    def find_nearest_index(array: np.ndarray, value: float) -> int:
        """
        Find the index of the nearest value in an array.

        Args:
            array: Input array to search
            value: Target value to find

        Returns:
            Index of nearest value

        Raises:
            ValueError: If array is empty or invalid
        """
        if not isinstance(array, np.ndarray) or array.size == 0:
            raise ValueError("Array must be a non-empty numpy array")

        return int(np.abs(array - value).argmin())

    @staticmethod
    def merge_well_data_arrays(
        depth_arrays: List[np.ndarray],
        dt_arrays: List[np.ndarray],
        dts_arrays: List[np.ndarray],
        rhob_arrays: List[np.ndarray],
        target_arrays: List[np.ndarray],
        well_names: Optional[List[str]] = None
    ) -> Dict[str, np.ndarray]:
        """
        Pure data merging function - returns processed arrays for UI to handle.

        Args:
            depth_arrays: List of depth arrays from each well
            dt_arrays: List of DT arrays from each well
            dts_arrays: List of DTS arrays from each well
            rhob_arrays: List of density arrays from each well
            target_arrays: List of target log arrays from each well
            well_names: Optional list of well names for logging

        Returns:
            Dict containing merged arrays:
                - depth: Merged depth array
                - dt: Merged DT array
                - dts: Merged DTS array
                - rhob: Merged density array
                - target: Merged target array
                - metadata: Dict with merge statistics

        Raises:
            ValueError: If input arrays are invalid or incompatible
        """
        try:
            # Input validation
            arrays_list = [depth_arrays, dt_arrays, dts_arrays, rhob_arrays, target_arrays]
            if not all(isinstance(arr_list, list) for arr_list in arrays_list):
                raise ValueError("All inputs must be lists of numpy arrays")

            if not all(len(arr_list) == len(depth_arrays) for arr_list in arrays_list):
                raise ValueError("All input lists must have the same length")

            if len(depth_arrays) == 0:
                raise ValueError("Input lists cannot be empty")

            # Initialize merged arrays
            merged_depth = []
            merged_dt = []
            merged_dts = []
            merged_rhob = []
            merged_target = []

            merge_stats = {
                'wells_processed': 0,
                'total_points': 0,
                'wells_skipped': [],
                'points_per_well': []
            }

            # Process each well
            for i, (depth, dt, dts, rhob, target) in enumerate(
                zip(depth_arrays, dt_arrays, dts_arrays, rhob_arrays, target_arrays)
            ):
                well_name = well_names[i] if well_names else f"Well_{i+1}"

                try:
                    # Validate individual arrays
                    if not all(isinstance(arr, np.ndarray) for arr in [depth, dt, dts, rhob, target]):
                        logger.warning(f"Skipping {well_name}: Non-numpy array inputs")
                        merge_stats['wells_skipped'].append(well_name)
                        continue

                    if not all(arr.size > 0 for arr in [depth, dt, dts, rhob, target]):
                        logger.warning(f"Skipping {well_name}: Empty arrays")
                        merge_stats['wells_skipped'].append(well_name)
                        continue

                    if not all(arr.shape == depth.shape for arr in [dt, dts, rhob, target]):
                        logger.warning(f"Skipping {well_name}: Inconsistent array shapes")
                        merge_stats['wells_skipped'].append(well_name)
                        continue

                    # Merge data
                    points_added = len(depth)
                    merged_depth.extend(depth)
                    merged_dt.extend(dt)
                    merged_dts.extend(dts)
                    merged_rhob.extend(rhob)
                    merged_target.extend(target)

                    merge_stats['wells_processed'] += 1
                    merge_stats['total_points'] += points_added
                    merge_stats['points_per_well'].append((well_name, points_added))

                    logger.info(f"Merged {points_added} points from {well_name}")

                except Exception as e:
                    logger.error(f"Error processing {well_name}: {str(e)}")
                    merge_stats['wells_skipped'].append(well_name)
                    continue

            # Validate merged results
            if not merged_depth:
                raise ValueError("No valid data could be merged from any well")

            # Convert to numpy arrays
            result = {
                'depth': np.array(merged_depth),
                'dt': np.array(merged_dt),
                'dts': np.array(merged_dts),
                'rhob': np.array(merged_rhob),
                'target': np.array(merged_target),
                'metadata': merge_stats
            }

            logger.info(f"Data merge complete: {merge_stats['wells_processed']} wells, {merge_stats['total_points']} total points")

            return result

        except Exception as e:
            logger.error(f"Data merge failed: {str(e)}")
            raise ValueError(f"Data merge failed: {str(e)}") from e

    @staticmethod
    def validate_array_compatibility(
        arrays: List[np.ndarray],
        array_names: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Validate that arrays are compatible for analysis.

        Args:
            arrays: List of numpy arrays to validate
            array_names: Optional names for arrays (for error reporting)

        Returns:
            Dict containing validation results:
                - valid: Boolean indicating if all arrays are compatible
                - errors: List of error messages
                - warnings: List of warning messages
                - statistics: Dict with array statistics
        """
        errors = []
        warnings = []
        statistics = {}

        try:
            if not arrays:
                errors.append("No arrays provided for validation")
                return {'valid': False, 'errors': errors, 'warnings': warnings, 'statistics': statistics}

            names = array_names if array_names else [f"Array_{i+1}" for i in range(len(arrays))]

            # Check array types and basic properties
            for i, (arr, name) in enumerate(zip(arrays, names)):
                if not isinstance(arr, np.ndarray):
                    errors.append(f"{name} is not a numpy array")
                    continue

                if arr.size == 0:
                    errors.append(f"{name} is empty")
                    continue

                # Calculate statistics
                finite_mask = np.isfinite(arr)
                finite_count = np.sum(finite_mask)

                statistics[name] = {
                    'shape': arr.shape,
                    'size': arr.size,
                    'finite_count': finite_count,
                    'finite_percentage': (finite_count / arr.size) * 100 if arr.size > 0 else 0,
                    'dtype': str(arr.dtype)
                }

                if finite_count == 0:
                    errors.append(f"{name} contains no finite values")
                elif finite_count < arr.size * 0.5:
                    warnings.append(f"{name} has less than 50% finite values ({finite_count}/{arr.size})")

            # Check shape compatibility
            if len(arrays) > 1:
                reference_shape = arrays[0].shape
                for i, (arr, name) in enumerate(zip(arrays[1:], names[1:]), 1):
                    if isinstance(arr, np.ndarray) and arr.shape != reference_shape:
                        errors.append(f"{name} shape {arr.shape} doesn't match reference shape {reference_shape}")

            is_valid = len(errors) == 0

            return {
                'valid': is_valid,
                'errors': errors,
                'warnings': warnings,
                'statistics': statistics
            }

        except Exception as e:
            logger.error(f"Array validation failed: {str(e)}")
            return {
                'valid': False,
                'errors': [f"Validation failed: {str(e)}"],
                'warnings': warnings,
                'statistics': statistics
            }

    @staticmethod
    def calculate_array_statistics(
        array: np.ndarray,
        percentiles: Optional[List[float]] = None
    ) -> Dict[str, float]:
        """
        Calculate comprehensive statistics for an array.

        Args:
            array: Input numpy array
            percentiles: List of percentiles to calculate (default: [2, 25, 50, 75, 98])

        Returns:
            Dict containing statistical measures
        """
        if percentiles is None:
            percentiles = [2, 25, 50, 75, 98]

        try:
            # Remove non-finite values
            finite_data = array[np.isfinite(array)]

            if len(finite_data) == 0:
                return {
                    'count': 0,
                    'finite_count': 0,
                    'mean': np.nan,
                    'std': np.nan,
                    'min': np.nan,
                    'max': np.nan,
                    **{f'p{p}': np.nan for p in percentiles}
                }

            stats = {
                'count': len(array),
                'finite_count': len(finite_data),
                'mean': np.mean(finite_data),
                'std': np.std(finite_data),
                'min': np.min(finite_data),
                'max': np.max(finite_data)
            }

            # Add percentiles
            for p in percentiles:
                stats[f'p{p}'] = np.percentile(finite_data, p)

            return stats

        except Exception as e:
            logger.error(f"Statistics calculation failed: {str(e)}")
            return {
                'count': len(array) if hasattr(array, '__len__') else 0,
                'finite_count': 0,
                'mean': np.nan,
                'std': np.nan,
                'min': np.nan,
                'max': np.nan,
                **{f'p{p}': np.nan for p in percentiles}
            }
